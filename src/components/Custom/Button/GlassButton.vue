<template>
  <button
    :type="htmlType"
    :disabled="disabled"
    :class="['glass-button', { 'is-loading': loading }, buttonWrapClass]"
    @click="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <span class="button-content">
      <Icon
        v-if="loading"
        icon="ant-design:loading-outlined"
        :size="iconSize"
        class="loading-icon"
      />
      <Icon v-if="preIcon && !loading" :icon="preIcon" :size="iconSize" class="button-icon" />
      <slot></slot>
      <Icon v-if="postIcon && !loading" :icon="postIcon" :size="iconSize" class="button-icon" />
    </span>
    <div class="button-shadow"></div>
  </button>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import Icon from '@/components/Icon/Icon.vue';

  defineOptions({
    name: 'GlassButton',
  });

  interface Props {
    /** 按钮类型 */
    type?: 'button' | 'submit' | 'reset';
    /** 前置图标 */
    preIcon?: string;
    /** 后置图标 */
    postIcon?: string;
    /** 图标大小 */
    iconSize?: number;
    /** 是否禁用 */
    disabled?: boolean;
    /** 是否加载中 */
    loading?: boolean;
    /** 按钮大小 */
    size?: 'small' | 'default' | 'large';
  }

  const props = withDefaults(defineProps<Props>(), {
    type: 'button',
    iconSize: 12,
    disabled: false,
    loading: false,
    size: 'default',
  });

  const emit = defineEmits<{
    click: [event: MouseEvent];
  }>();

  const htmlType = computed(() => props.type);

  const handleClick = (event: MouseEvent) => {
    if (props.disabled || props.loading) {
      return;
    }
    console.log('GlassButton props:', {
      preIcon: props.preIcon,
      postIcon: props.postIcon,
      iconSize: props.iconSize,
      loading: props.loading,
      disabled: props.disabled,
    });
    emit('click', event);
  };

  // 为不支持 :has() 的浏览器提供备用方案
  const handleMouseEnter = (event: MouseEvent) => {
    const button = event.currentTarget as HTMLElement;
    if (button && !props.disabled && !props.loading) {
      button.classList.add('hover-active');
    }
  };

  const handleMouseLeave = (event: MouseEvent) => {
    const button = event.currentTarget as HTMLElement;
    if (button) {
      button.classList.remove('hover-active');
    }
  };

  // 计算按钮的类
  const buttonWrapClass = computed(() => ({
    disabled: props.disabled,
    loading: props.loading,
  }));
</script>

<style scoped>
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }

  /* Fallback for browsers without :has() support */
  @supports not selector(:has(*)) {
    .button-wrap:hover .button-shadow,
    .button-wrap.hover-active .button-shadow {
      filter: blur(clamp(2px, 1px, 6px));
    }

    .button-wrap:hover .button-shadow::after,
    .button-wrap.hover-active .button-shadow::after {
      top: calc(var(--shadow-cutoff-fix) - 10px);
      opacity: 1;
    }
  }

  /* Touch Devices */
  @media (hover: none) and (pointer: coarse) {
    .glass-button .button-content::after,
    .glass-button:active .button-content::after {
      --angle-2: -45deg;
    }
  }

  @media (hover: none) and (pointer: coarse) {
    .glass-button::after,
    .glass-button:hover::after,
    .glass-button:active::after {
      --angle-1: -75deg;
    }
  }

  /* Glass Button */
  .glass-button {
    /* CSS Custom Properties */
    --angle-1: -75deg;
    --angle-2: -45deg;
    --global-size: clamp(32px, 4vw, 80px);
    --anim-hover-time: 400ms;
    --anim-hover-ease: cubic-bezier(0.25, 1, 0.5, 1);
    --border-width: clamp(1px, 1px, 4px);
    --shadow-cutoff-fix: 24px;

    /* Glass Button Styling */
    display: inline-block;
    position: relative;
    z-index: 2;

    /* Reset default button styles */
    margin: 0;
    padding: 0;
    transition: all var(--anim-hover-time) var(--anim-hover-ease);
    border: none;
    border-radius: 999vw;
    outline: none;
    background: linear-gradient(
      -75deg,
      rgb(255 255 255 / 5%),
      rgb(255 255 255 / 20%),
      rgb(255 255 255 / 5%)
    );
    box-shadow:
      inset 0 2px 2px rgb(0 0 0 / 5%),
      inset 0 -2px 2px rgb(255 255 255 / 50%),
      0 4px 2px -2px rgb(0 0 0 / 20%),
      0 0 1.6px 4px inset rgb(255 255 255 / 20%),
      0 0 0 0 rgb(255 255 255 / 100%);
    color: inherit;
    font: inherit;
    text-decoration: none;
    cursor: pointer;
    pointer-events: auto;
    -webkit-tap-highlight-color: rgb(0 0 0 / 0%);
    backdrop-filter: blur(clamp(1px, 2px, 4px));
  }

  /* ========== BUTTON ========== */

  /* Button Shadow Container */
  .button-shadow {
    position: absolute;
    top: calc(0% - var(--shadow-cutoff-fix) / 2);
    left: calc(0% - var(--shadow-cutoff-fix) / 2);
    width: calc(100% + var(--shadow-cutoff-fix));
    height: calc(100% + var(--shadow-cutoff-fix));
    overflow: visible;
    pointer-events: none;
    filter: blur(clamp(2px, 2px, 12px));
  }

  /* Shadow */
  .button-shadow::after {
    content: '';
    position: absolute;
    z-index: 0;
    top: calc(var(--shadow-cutoff-fix) - 6px);
    left: calc(var(--shadow-cutoff-fix) - 10px);
    box-sizing: border-box;
    width: calc(100% - var(--shadow-cutoff-fix) - 3px);
    height: calc(100% - var(--shadow-cutoff-fix) - 3px);
    padding: 2px;
    overflow: visible;
    transition: all var(--anim-hover-time) var(--anim-hover-ease);
    border-radius: 999vw;
    opacity: 1;
    background: linear-gradient(180deg, rgb(0 0 0 / 20%), rgb(0 0 0 / 10%));
    mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    mask-composite: exclude;
  }

  .glass-button:disabled {
    transform: none !important;
    opacity: 0.6;
    cursor: not-allowed;
  }

  .glass-button.is-loading {
    cursor: default;
  }

  .glass-button:hover:not(:disabled):not(.is-loading) {
    transform: scale(0.975) !important;
    box-shadow:
      inset 0 2px 2px rgb(0 0 0 / 5%),
      inset 0 -2px 2px rgb(255 255 255 / 50%),
      0 2.4px 0.8px -1.6px rgb(0 0 0 / 25%),
      0 0 0.8px 1.6px inset rgb(255 255 255 / 50%),
      0 0 0 0 rgb(255 255 255 / 100%);
    backdrop-filter: blur(0.16px);
  }

  /* 简化的 hover 效果，确保能工作 */
  .glass-button:hover {
    transform: scale(0.95);
    opacity: 0.8;
  }

  /* Button Text */
  .button-content {
    display: flex;
    position: relative;
    z-index: 4; /* 确保内容在伪元素之上 */
    align-items: center;
    justify-content: center;
    padding-block: 8px;
    padding-inline: 16px;
    transition: all var(--anim-hover-time) var(--anim-hover-ease);
    color: rgb(50 50 50 / 100%);
    font-family: Inter, sans-serif;
    font-size: 14px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-weight: 500;
    letter-spacing: 0;
    text-shadow: 0 4px 0.8px rgb(0 0 0 / 10%);
    user-select: none;
    gap: 6px;
  }

  .glass-button:hover:not(:disabled):not(.is-loading) .button-content {
    text-shadow: 0.4px 0.4px 0.4px rgb(0 0 0 / 12%);
  }

  /* Loading and Icon Styles */
  .loading-icon,
  .button-icon {
    display: inline-flex;
    position: relative;
    z-index: 4; /* 确保图标在伪元素之上 */
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    animation: none;
    line-height: 1;
    vertical-align: baseline;
  }

  .loading-icon {
    animation: spin 1s linear infinite;
  }

  /* Text */
  .button-content::after {
    content: '';
    display: block;
    position: absolute;
    z-index: 1; /* 降低 z-index，让图标和文本在上面 */
    top: calc(0% + var(--border-width) / 2);
    left: calc(0% + var(--border-width) / 2);
    box-sizing: border-box;
    width: calc(100% - var(--border-width)); /* Prevent overlapping border */
    height: calc(100% - var(--border-width));
    overflow: visible; /* 改为 visible，避免裁剪图标 */
    transition:
      background-position calc(var(--anim-hover-time) * 1.25) var(--anim-hover-ease),
      --angle-2 calc(var(--anim-hover-time) * 1.25) var(--anim-hover-ease);
    border-radius: 999vw;
    background: linear-gradient(
      var(--angle-2),
      rgb(255 255 255 / 0%) 0%,
      rgb(255 255 255 / 50%) 40% 50%,
      rgb(255 255 255 / 0%) 55%
    );
    background-repeat: no-repeat;
    background-position: 0% 50%;
    background-size: 200% 200%;
    pointer-events: none;
    mix-blend-mode: screen;
  }

  .glass-button:hover:not(:disabled):not(.is-loading) .button-content::after {
    background-position: 25% 50%;
  }

  .glass-button:active:not(:disabled):not(.is-loading) .button-content::after {
    --angle-2: -15deg;

    background-position: 50% 15%;
  }

  /* ========== BUTTON OUTLINE ========== */

  /* Outline */
  .glass-button::after {
    content: '';
    position: absolute;
    z-index: 1;
    top: calc(0% - var(--border-width) / 2);
    left: calc(0% - var(--border-width) / 2);
    box-sizing: border-box;
    width: calc(100% + var(--border-width));
    height: calc(100% + var(--border-width));
    padding: var(--border-width);
    transition:
      all var(--anim-hover-time) var(--anim-hover-ease),
      --angle-1 500ms ease;
    border-radius: 999vw;
    background:
      conic-gradient(
        from var(--angle-1) at 50% 50%,
        rgb(0 0 0 / 50%),
        rgb(0 0 0 / 0%) 5% 40%,
        rgb(0 0 0 / 50%) 50%,
        rgb(0 0 0 / 0%) 60% 95%,
        rgb(0 0 0 / 50%)
      ),
      linear-gradient(180deg, rgb(255 255 255 / 50%), rgb(255 255 255 / 50%));
    box-shadow: inset 0 0 0 calc(var(--border-width) / 2) rgb(255 255 255 / 50%);
    mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    mask-composite: exclude;
  }

  .glass-button:hover:not(:disabled):not(.is-loading)::after {
    --angle-1: -125deg;
  }

  .glass-button:active:not(:disabled):not(.is-loading)::after {
    --angle-1: -75deg;
  }

  /* Shadow Hover Effects */
  .glass-button:hover .button-shadow,
  .glass-button.hover-active .button-shadow {
    transition: filter var(--anim-hover-time) var(--anim-hover-ease);
    filter: blur(clamp(2px, 1px, 6px));
  }

  .glass-button:hover .button-shadow::after,
  .glass-button.hover-active .button-shadow::after {
    top: calc(var(--shadow-cutoff-fix) - 10px);
    opacity: 1;
  }

  /* Active State Effects */
  .glass-button:active {
    transform: rotate3d(1, 0, 0, 25deg);
  }

  .glass-button:active:not(:disabled):not(.is-loading) {
    box-shadow:
      inset 0 2px 2px rgb(0 0 0 / 5%),
      inset 0 -2px 2px rgb(255 255 255 / 50%),
      0 2px 2px -2px rgb(0 0 0 / 20%),
      0 0 1.6px 4px inset rgb(255 255 255 / 20%),
      0 3.6px 0.8px 0 rgb(0 0 0 / 5%),
      0 4px 0 0 rgb(255 255 255 / 75%),
      inset 0 4px 0.8px 0 rgb(0 0 0 / 15%);
  }

  .glass-button:active .button-shadow {
    filter: blur(clamp(2px, 2px, 12px));
  }

  .glass-button:active .button-shadow::after {
    top: calc(var(--shadow-cutoff-fix) - 6px);
    opacity: 0.75;
  }

  .glass-button:active .button-content {
    text-shadow: 0.4px 4px 0.8px rgb(0 0 0 / 12%);
  }

  /* Disabled and Loading States */
  .glass-button.disabled,
  .glass-button.loading {
    pointer-events: none;
  }

  .glass-button.disabled:hover,
  .glass-button.loading:hover,
  .glass-button.disabled:active,
  .glass-button.loading:active {
    transform: none !important;
  }
</style>
