import { ImportFileProps } from './ImportFile/type';
import { withInstall } from '@/utils';
import { defineAsyncComponent } from 'vue';
import { createFuncComp } from '@/utils/compFn';

export * from './types/basicSelectTree';

export { default as BasicSelectTree } from './BasicSelectTree.vue';
export { default as BasicSelectTreeItem } from './BasicSelectTreeItem.vue';

export const ImportFileFn = createFuncComp<ImportFileProps>(
  withInstall(defineAsyncComponent(() => import('./ImportFile/ImportFile.vue'))),
);

export { StatisticCard, useStatisticCard } from './StatisticCard';
export * from './StatisticCard';
