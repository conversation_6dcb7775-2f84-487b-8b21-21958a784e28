import type { Ref } from 'vue';
import { ref, onUnmounted, unref, nextTick } from 'vue';
import type {
  UseStatisticCardReturnType,
  StatisticCardInstance,
  StatisticCardProps,
} from './typing';

export function useStatisticCard(
  props?: Ref<Partial<StatisticCardProps>> | Partial<StatisticCardProps>,
): UseStatisticCardReturnType {
  const statisticCardRef = ref<Nullable<StatisticCardInstance>>(null);
  const loadedRef = ref<Nullable<boolean>>(false);

  function register(instance: StatisticCardInstance) {
    onUnmounted(() => {
      statisticCardRef.value = null;
      loadedRef.value = null;
    });

    if (unref(loadedRef) && instance === unref(statisticCardRef)) {
      return;
    }

    statisticCardRef.value = instance;
    loadedRef.value = true;

    if (props) {
      nextTick(() => {
        instance.setStatisticProps(unref(props));
      });
    }
  }

  const methods: StatisticCardInstance = {
    setStatisticProps: (statisticProps: Partial<StatisticCardProps>): void => {
      unref(statisticCardRef)?.setStatisticProps(statisticProps);
    },
  };

  return [register, methods];
}
