import type { VNode, CSSProperties, Ref } from 'vue';

export interface StatisticItem {
  // 字段名
  field: string;
  // 标题
  title: string | VNode | JSX.Element;
  // 值
  value: number | string;
  // 前缀
  prefix?: string;
  // 后缀
  suffix?: string;
  // 图标
  icon?: string | VNode | JSX.Element;
  // 图标颜色
  iconColor?: string;
  // 数值颜色
  valueColor?: string;
  // 标题颜色
  titleColor?: string;
  // 是否启用数字动画
  useAnimation?: boolean;
  // 动画持续时间
  duration?: number;
  // 小数位数
  decimals?: number;
  // 千分位分隔符
  separator?: string;
  // 小数点符号
  decimal?: string;
  // 是否显示
  show?: (...arg: any) => boolean;
  // 自定义渲染
  render?: (
    val: any,
    data: Recordable,
  ) => VNode | undefined | JSX.Element | Element | string | number | Ref<string | number>;
  // 自定义样式
  style?: CSSProperties;
  // 卡片类名
  className?: string;
  // 点击事件
  onClick?: (item: StatisticItem, data: Recordable) => void;

  // === 新增字段 ===
  // 统计单位/标签 (如: 月、周、年)
  unit?: string;
  // 单位颜色
  unitColor?: string;
  // 底部数据
  footer?: {
    // 底部标题
    title?: string;
    // 底部值
    value?: number | string;
    // 底部字段名
    field?: string;
    // 底部前缀
    prefix?: string;
    // 底部后缀
    suffix?: string;
    // 底部颜色
    color?: string;
    // 千分位分隔符
    separator?: string;
    // 小数点符号
    decimal?: string;
    // 小数位数
    decimals?: number;
    // 底部自定义渲染
    render?: (val: any, data: Recordable) => string | number;
  };
  // 主题色
  theme?: 'blue' | 'green' | 'orange' | 'red' | 'purple' | 'cyan' | string;
}

export interface StatisticCardProps {
  // 统计项配置
  schema: StatisticItem[];
  // 数据源
  data?: Recordable;
  // 列数配置
  column?: number | Record<string, number>;
  // 间距
  gap?: number | string;
  // 是否显示边框
  bordered?: boolean;
  // 卡片圆角
  borderRadius?: number | string;
  // 卡片阴影
  shadow?: string;
  // 卡片背景色
  backgroundColor?: string;
  // 卡片内边距
  padding?: number | string;
  // 最小高度
  minHeight?: number | string;
}

export interface StatisticCardInstance {
  setStatisticProps(props: Partial<StatisticCardProps>): void;
}

export type Register = (instance: StatisticCardInstance) => void;

export type UseStatisticCardReturnType = [Register, StatisticCardInstance];
