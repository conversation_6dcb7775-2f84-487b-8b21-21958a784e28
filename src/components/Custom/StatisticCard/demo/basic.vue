<template>
  <div class="p-4">
    <h2 class="mb-4 text-xl font-semibold">统计卡片组件示例</h2>
    
    <!-- 基础用法 -->
    <div class="mb-8">
      <h3 class="mb-4 text-lg font-medium">基础用法</h3>
      <StatisticCard :schema="basicSchema" :data="statisticData" />
    </div>

    <!-- 自定义样式 -->
    <div class="mb-8">
      <h3 class="mb-4 text-lg font-medium">自定义样式</h3>
      <StatisticCard 
        :schema="customSchema" 
        :data="statisticData"
        :column="3"
        :gap="20"
        border-radius="12"
        shadow="0 4px 20px rgba(0, 0, 0, 0.12)"
        background-color="#f8fafc"
      />
    </div>

    <!-- 使用 Hook -->
    <div class="mb-8">
      <h3 class="mb-4 text-lg font-medium">使用 Hook 动态更新</h3>
      <div class="mb-4">
        <a-button type="primary" @click="updateData">更新数据</a-button>
        <a-button class="ml-2" @click="toggleAnimation">
          {{ animationEnabled ? '关闭' : '开启' }}动画
        </a-button>
      </div>
      <StatisticCard @register="register" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { StatisticCard, useStatisticCard } from '@/components/Custom';
import type { StatisticItem } from '@/components/Custom';
import { Button } from 'ant-design-vue';

// 基础数据
const statisticData = reactive({
  totalUsers: 1000,
  activeUsers: 750,
  newUsers: 100,
  revenue: 50000,
  orders: 2580,
  conversion: 3.2,
});

// 基础配置
const basicSchema: StatisticItem[] = [
  {
    field: 'totalUsers',
    title: '总用户数',
    value: 'totalUsers',
    icon: 'ant-design:user-outlined',
    iconColor: '#1890ff',
    valueColor: '#1890ff',
  },
  {
    field: 'activeUsers',
    title: '活跃用户',
    value: 'activeUsers',
    icon: 'ant-design:team-outlined',
    iconColor: '#52c41a',
    valueColor: '#52c41a',
  },
  {
    field: 'newUsers',
    title: '新增用户',
    value: 'newUsers',
    icon: 'ant-design:user-add-outlined',
    iconColor: '#faad14',
    valueColor: '#faad14',
  },
  {
    field: 'revenue',
    title: '总收入',
    value: 'revenue',
    icon: 'ant-design:dollar-outlined',
    iconColor: '#f5222d',
    valueColor: '#f5222d',
    prefix: '¥',
    decimals: 2,
  },
];

// 自定义样式配置
const customSchema: StatisticItem[] = [
  {
    field: 'orders',
    title: '订单总数',
    value: 'orders',
    icon: 'ant-design:shopping-cart-outlined',
    iconColor: '#722ed1',
    valueColor: '#722ed1',
    duration: 2000,
  },
  {
    field: 'conversion',
    title: '转化率',
    value: 'conversion',
    icon: 'ant-design:rise-outlined',
    iconColor: '#13c2c2',
    valueColor: '#13c2c2',
    suffix: '%',
    decimals: 1,
    duration: 2500,
  },
  {
    field: 'revenue',
    title: '月收入',
    value: 'revenue',
    icon: 'ant-design:fund-outlined',
    iconColor: '#eb2f96',
    valueColor: '#eb2f96',
    prefix: '¥',
    decimals: 0,
    render: (val) => {
      return `${(val / 10000).toFixed(1)}万`;
    },
  },
];

// Hook 使用
const [register, { setStatisticProps }] = useStatisticCard();
const animationEnabled = ref(true);

// 初始化 Hook 配置
setStatisticProps({
  schema: basicSchema,
  data: statisticData,
  column: 4,
});

// 更新数据
function updateData() {
  const newData = {
    totalUsers: Math.floor(Math.random() * 5000) + 1000,
    activeUsers: Math.floor(Math.random() * 3000) + 500,
    newUsers: Math.floor(Math.random() * 500) + 50,
    revenue: Math.floor(Math.random() * 100000) + 20000,
    orders: Math.floor(Math.random() * 5000) + 1000,
    conversion: Math.random() * 10 + 1,
  };
  
  Object.assign(statisticData, newData);
  
  setStatisticProps({
    data: newData,
  });
}

// 切换动画
function toggleAnimation() {
  animationEnabled.value = !animationEnabled.value;
  
  const updatedSchema = basicSchema.map(item => ({
    ...item,
    useAnimation: animationEnabled.value,
  }));
  
  setStatisticProps({
    schema: updatedSchema,
  });
}
</script>

<style scoped>
.p-4 {
  padding: 1rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-lg {
  font-size: 1.125rem;
}

.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}
</style>
