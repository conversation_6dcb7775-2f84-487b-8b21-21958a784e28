# StatisticCard 统计卡片组件

一个美观的统计数据展示组件，支持数字动画效果和自定义渲染。

## 功能特性

- 🎯 **数字动画**: 内置数字滚动动画效果
- 🎨 **美观设计**: 简约现代的卡片设计
- 📱 **响应式**: 支持多种屏幕尺寸自适应
- 🔧 **高度可定制**: 支持自定义图标、颜色、样式等
- 📊 **多种布局**: 支持网格布局，可配置列数
- 🎭 **自定义渲染**: 支持自定义内容渲染函数

## 基本用法

```vue
<template>
  <StatisticCard :schema="statisticSchema" :data="statisticData" />
</template>

<script setup lang="ts">
  import { StatisticCard } from '@/components/Custom';
  import type { StatisticItem } from '@/components/Custom';

  const statisticData = {
    totalUsers: 1000,
    activeUsers: 750,
    newUsers: 100,
    revenue: 50000,
  };

  const statisticSchema: StatisticItem[] = [
    {
      field: 'totalUsers',
      title: '总用户数',
      value: 'totalUsers',
      icon: 'ant-design:user-outlined',
      iconColor: '#1890ff',
      valueColor: '#1890ff',
      prefix: '',
      suffix: '',
      useAnimation: true,
      duration: 2000,
    },
    {
      field: 'activeUsers',
      title: '活跃用户',
      value: 'activeUsers',
      icon: 'ant-design:team-outlined',
      iconColor: '#52c41a',
      valueColor: '#52c41a',
      useAnimation: true,
    },
    {
      field: 'newUsers',
      title: '新增用户',
      value: 'newUsers',
      icon: 'ant-design:user-add-outlined',
      iconColor: '#faad14',
      valueColor: '#faad14',
      useAnimation: true,
    },
    {
      field: 'revenue',
      title: '总收入',
      value: 'revenue',
      icon: 'ant-design:dollar-outlined',
      iconColor: '#f5222d',
      valueColor: '#f5222d',
      prefix: '¥',
      decimals: 2,
      useAnimation: true,
    },
  ];
</script>
```

## 使用 Hook

```vue
<template>
  <StatisticCard @register="register" />
</template>

<script setup lang="ts">
  import { StatisticCard, useStatisticCard } from '@/components/Custom';
  import type { StatisticItem } from '@/components/Custom';

  const [register, { setStatisticProps }] = useStatisticCard();

  // 动态更新配置
  function updateStatistics() {
    setStatisticProps({
      schema: newSchema,
      data: newData,
    });
  }
</script>
```

## API

### StatisticCard Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| schema | 统计项配置数组 | `StatisticItem[]` | `[]` |
| data | 数据源 | `Record<string, any>` | `{}` |
| column | 列数配置 | `number \| Record<string, number>` | `{ xxl: 4, xl: 3, lg: 3, md: 2, sm: 2, xs: 1 }` |
| gap | 间距 | `number \| string` | `16` |
| bordered | 是否显示边框 | `boolean` | `true` |
| borderRadius | 卡片圆角 | `number \| string` | `8` |
| shadow | 卡片阴影 | `string` | `'0 2px 8px rgba(0, 0, 0, 0.1)'` |
| backgroundColor | 卡片背景色 | `string` | `'#ffffff'` |
| padding | 卡片内边距 | `number \| string` | `20` |
| minHeight | 最小高度 | `number \| string` | `120` |

### StatisticItem

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| field | 字段名 | `string` | - |
| title | 标题 | `string \| VNode \| JSX.Element` | - |
| value | 值 | `number \| string` | - |
| icon | 图标 | `string \| VNode \| JSX.Element` | - |
| iconColor | 图标颜色 | `string` | - |
| valueColor | 数值颜色 | `string` | - |
| titleColor | 标题颜色 | `string` | - |
| prefix | 前缀 | `string` | - |
| suffix | 后缀 | `string` | - |
| useAnimation | 是否启用数字动画 | `boolean` | `true` |
| duration | 动画持续时间 | `number` | `1500` |
| decimals | 小数位数 | `number` | `0` |
| separator | 千分位分隔符 | `string` | `','` |
| decimal | 小数点符号 | `string` | `'.'` |
| show | 是否显示 | `(...arg: any) => boolean` | - |
| render | 自定义渲染 | `(val: any, data: Record<string, any>) => VNode \| string \| number` | - |
| style | 自定义样式 | `CSSProperties` | - |
| className | 卡片类名 | `string` | - |
| onClick | 点击事件 | `(item: StatisticItem, data: Record<string, any>) => void` | - |

## 样式定制

组件使用 CSS 变量，可以通过覆盖变量来定制样式：

```less
.vben-statistic-card {
  &__value {
    font-size: 32px; // 自定义数值字体大小
  }

  &__title {
    color: #666; // 自定义标题颜色
  }

  &__item {
    &:hover {
      transform: translateY(-4px); // 自定义悬停效果
    }
  }
}
```

## 完整示例

查看 `example.vue` 文件获取完整的使用示例，包括：

- 基础用法
- 自定义样式
- 响应式布局
- 动态数据更新
- 动画控制

## 注意事项

1. **数据格式**: 确保传入的数值是有效的数字格式
2. **图标**: 使用 Ant Design 图标库的图标名称
3. **响应式**: 组件内置响应式支持，会根据屏幕大小自动调整布局
4. **性能**: 大量数据时建议关闭动画以提升性能
5. **自定义渲染**: 使用 render 函数时，确保返回值是有效的 Vue 组件或字符串

## 更新日志

### v1.0.0

- 初始版本发布
- 支持基础统计卡片展示
- 支持数字动画效果
- 支持自定义图标和颜色
- 支持响应式布局
