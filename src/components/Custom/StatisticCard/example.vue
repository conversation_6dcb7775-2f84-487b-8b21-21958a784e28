<template>
  <div class="p-6 bg-gray-50 min-h-screen">
    <h1 class="text-2xl font-bold mb-6">统计卡片组件示例</h1>

    <!-- 基础示例 -->
    <div class="mb-8">
      <h2 class="text-lg font-semibold mb-4">基础示例</h2>
      <StatisticCard :schema="basicSchema" :data="statisticData" />
    </div>

    <!-- 自定义颜色和图标 -->
    <div class="mb-8">
      <h2 class="text-lg font-semibold mb-4">自定义颜色和图标</h2>
      <StatisticCard
        :schema="colorfulSchema"
        :data="statisticData"
        :column="3"
        :gap="24"
        border-radius="12"
        shadow="0 8px 32px rgba(0, 0, 0, 0.1)"
      />
    </div>

    <!-- 响应式布局 -->
    <div class="mb-8">
      <h2 class="text-lg font-semibold mb-4">响应式布局</h2>
      <StatisticCard
        :schema="responsiveSchema"
        :data="statisticData"
        :column="{ xxl: 5, xl: 4, lg: 3, md: 2, sm: 2, xs: 1 }"
      />
    </div>

    <!-- 控制按钮 -->
    <div class="mb-4 space-x-4">
      <button
        @click="refreshData"
        class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        刷新数据
      </button>
      <button
        @click="toggleAnimation"
        class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
      >
        {{ animationEnabled ? '关闭' : '开启' }}动画
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import { StatisticCard } from '@/components/Custom';
  import type { StatisticItem } from '@/components/Custom';

  // 响应式数据
  const statisticData = reactive({
    totalUsers: 1000,
    activeUsers: 750,
    newUsers: 100,
    revenue: 50000,
    orders: 2580,
    conversion: 3.2,
    growth: 15.8,
    satisfaction: 98.5,
  });

  const animationEnabled = ref(true);

  // 基础配置
  const basicSchema: StatisticItem[] = [
    {
      field: 'totalUsers',
      title: '总用户数',
      value: 'totalUsers',
      icon: 'ant-design:user-outlined',
      iconColor: '#1890ff',
      valueColor: '#1890ff',
    },
    {
      field: 'activeUsers',
      title: '活跃用户',
      value: 'activeUsers',
      icon: 'ant-design:team-outlined',
      iconColor: '#52c41a',
      valueColor: '#52c41a',
    },
    {
      field: 'newUsers',
      title: '新增用户',
      value: 'newUsers',
      icon: 'ant-design:user-add-outlined',
      iconColor: '#faad14',
      valueColor: '#faad14',
    },
    {
      field: 'revenue',
      title: '总收入',
      value: 'revenue',
      icon: 'ant-design:dollar-outlined',
      iconColor: '#f5222d',
      valueColor: '#f5222d',
      prefix: '¥',
      decimals: 2,
    },
  ];

  // 彩色配置
  const colorfulSchema: StatisticItem[] = [
    {
      field: 'orders',
      title: '订单总数',
      value: 'orders',
      icon: 'ant-design:shopping-cart-outlined',
      iconColor: '#722ed1',
      valueColor: '#722ed1',
      duration: 2000,
    },
    {
      field: 'conversion',
      title: '转化率',
      value: 'conversion',
      icon: 'ant-design:rise-outlined',
      iconColor: '#13c2c2',
      valueColor: '#13c2c2',
      suffix: '%',
      decimals: 1,
      duration: 2500,
    },
    {
      field: 'growth',
      title: '增长率',
      value: 'growth',
      icon: 'ant-design:arrow-up-outlined',
      iconColor: '#52c41a',
      valueColor: '#52c41a',
      suffix: '%',
      decimals: 1,
      duration: 1800,
    },
  ];

  // 响应式配置
  const responsiveSchema: StatisticItem[] = [
    {
      field: 'totalUsers',
      title: '用户',
      value: 'totalUsers',
      icon: 'ant-design:user-outlined',
      iconColor: '#1890ff',
      valueColor: '#1890ff',
    },
    {
      field: 'orders',
      title: '订单',
      value: 'orders',
      icon: 'ant-design:shopping-outlined',
      iconColor: '#722ed1',
      valueColor: '#722ed1',
    },
    {
      field: 'revenue',
      title: '收入',
      value: 'revenue',
      icon: 'ant-design:dollar-outlined',
      iconColor: '#f5222d',
      valueColor: '#f5222d',
      prefix: '¥',
      render: (val) => `${(val / 10000).toFixed(1)}万`,
    },
    {
      field: 'conversion',
      title: '转化',
      value: 'conversion',
      icon: 'ant-design:percentage-outlined',
      iconColor: '#13c2c2',
      valueColor: '#13c2c2',
      suffix: '%',
      decimals: 1,
    },
    {
      field: 'satisfaction',
      title: '满意度',
      value: 'satisfaction',
      icon: 'ant-design:smile-outlined',
      iconColor: '#52c41a',
      valueColor: '#52c41a',
      suffix: '%',
      decimals: 1,
    },
  ];

  // 刷新数据
  function refreshData() {
    Object.assign(statisticData, {
      totalUsers: Math.floor(Math.random() * 5000) + 1000,
      activeUsers: Math.floor(Math.random() * 3000) + 500,
      newUsers: Math.floor(Math.random() * 500) + 50,
      revenue: Math.floor(Math.random() * 100000) + 20000,
      orders: Math.floor(Math.random() * 5000) + 1000,
      conversion: Math.random() * 10 + 1,
      growth: Math.random() * 30 + 5,
      satisfaction: Math.random() * 10 + 90,
    });
  }

  // 切换动画
  function toggleAnimation() {
    animationEnabled.value = !animationEnabled.value;

    // 更新所有配置的动画状态
    [basicSchema, colorfulSchema, responsiveSchema].forEach((schema) => {
      schema.forEach((item) => {
        item.useAnimation = animationEnabled.value;
      });
    });
  }
</script>

<style scoped>
  .p-6 {
    padding: 1.5rem;
  }

  .bg-gray-50 {
    background-color: #f9fafb;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .text-2xl {
    font-size: 1.5rem;
  }

  .text-lg {
    font-size: 1.125rem;
  }

  .font-bold {
    font-weight: 700;
  }

  .font-semibold {
    font-weight: 600;
  }

  .mb-6 {
    margin-bottom: 1.5rem;
  }

  .mb-8 {
    margin-bottom: 2rem;
  }

  .mb-4 {
    margin-bottom: 1rem;
  }

  .space-x-4 > * + * {
    margin-left: 1rem;
  }

  .px-4 {
    padding-right: 1rem;
    padding-left: 1rem;
  }

  .py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .bg-blue-500 {
    background-color: #3b82f6;
  }

  .bg-green-500 {
    background-color: #10b981;
  }

  .text-white {
    color: white;
  }

  .rounded {
    border-radius: 0.25rem;
  }

  .hover\:bg-blue-600:hover {
    background-color: #2563eb;
  }

  .hover\:bg-green-600:hover {
    background-color: #059669;
  }
</style>
