import { DescItem } from '@/components/Description';
import { filePreview, showToBadge } from '@/components/RenderVnode';
import { calculateFileSize } from '@/utils/calculateFileSize';
import { FormSchema, BasicColumn } from '@/components/Table';
import { useMapWithI18n } from '@/hooks/web/useOnlineI18n';
import { UpgradeTypeList, UpgradeModelList, UpgradeStatusList } from '@/maps/prMaps';
import { apiGetCustomerPage } from '@/api/op/customer';
import { getAllPageData } from '@/utils/other';
import { apiInfoListByCustomerId } from '@/api/op/pr';
import { get } from 'lodash-es';
import { transformRangePicker } from '@/utils/formFn';

// 固件基础信息
export const basicInfoSchema: DescItem[] = [
  {
    field: 'name',
    label: '固件名称',
  },
  {
    field: 'productList',
    label: '支持设备型号',
    render: (_, record) => {
      if (record.productList && record.productList.length > 0) {
        return record.productList.map((product: any) => product.model).join(', ');
      }
      return '-';
    },
  },
  {
    field: 'version',
    label: '固件版本',
  },
  {
    field: 'signType',
    label: '签名方式',
  },
  {
    field: 'sign',
    label: '签名',
  },
  {
    field: 'file',
    label: '固件文件',
    render: (val: string) => {
      return filePreview(val);
    },
  },
  {
    field: 'size',
    label: '文件大小',
    render: (val: string) => {
      return calculateFileSize(val);
    },
  },
  {
    field: 'remark',
    label: '固件描述',
  },
  {
    field: 'createTime',
    label: '创建时间',
  },
  {
    field: 'createBy',
    label: '创建人',
  },
];

// 固件升级任务创建
export const taskSchema = (): FormSchema[] => [
  {
    field: 'name',
    label: '任务名称',
    fields: ['id', 'firmwareId'],
    component: 'Input',
    required: true,
  },
  {
    field: 'updateType',
    label: '升级设备筛选', // 按照型号 合作伙伴 终端客户
    component: 'RadioButtonGroup',
    componentProps: ({ formActionType }) => {
      return {
        options: useMapWithI18n(UpgradeTypeList),
        onChange: () => {
          formActionType.setFieldsValue({
            customerId: undefined,
          });
        },
      };
    },
    required: true,
    defaultValue: 'BY_MODEL',
  },
  {
    field: 'customerId',
    label: ({ model }) => {
      if (model.updateType === 'BY_PARTNER') {
        return '选择合作伙伴';
      } else if (model.updateType === 'BY_CUSTOMER') {
        return '选择终端客户';
      }
      return '';
    },
    component: 'ApiSelect',
    ifShow: ({ model }) => {
      return model.updateType === 'BY_PARTNER' || model.updateType === 'BY_CUSTOMER';
    },
    required({ model }) {
      return model.updateType === 'BY_PARTNER' || model.updateType === 'BY_CUSTOMER';
    },
    componentProps: ({ formModel }) => {
      return {
        api: (params) => getAllPageData(apiGetCustomerPage, params),
        params: {
          type: formModel?.updateType === 'BY_PARTNER' ? 'PARTNER' : 'CUSTOMER',
        },
        labelField: 'linkman',
        valueField: 'id',
      };
    },
  },
  {
    field: 'selectModel', /// 适配的全部型号 选择的设备型号升级
    label: '升级设备',
    component: 'RadioButtonGroup',
    componentProps: {
      options: useMapWithI18n(UpgradeModelList),
    },
    required: true,
    defaultValue: 'ALL_MODEL',
  },
  {
    field: 'productIds',
    label: '选择型号',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        placeholder: '请选择型号',
        api: (params) => {
          if (params?.firmwareId) {
            return apiInfoListByCustomerId(params?.firmwareId);
          }
          return Promise.resolve([]);
        },
        params: {
          firmwareId: formModel?.firmwareId,
        },
        labelField: 'model',
        valueField: 'id',
        mode: 'multiple',
      };
    },
    ifShow: ({ model }) => {
      return model.selectModel === 'SELECT_MODEL';
    },
    required: ({ model }) => {
      return model.selectModel === 'SELECT_MODEL';
    },
    transformGetTo: 'toArr',
    transformSetTo: 'toArr',
  },
  {
    field: 'remark',
    label: '任务描述',
    component: 'InputTextArea',
  },
];

// 固件升级任务列表
export const taskListSchema: BasicColumn[] = [
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
  {
    title: '任务ID',
    dataIndex: 'id',
  },
  {
    title: '任务名称',
    dataIndex: 'name',
  },
  {
    title: '操作人',
    dataIndex: 'createBy',
  },
  {
    title: '涉及设备数量',
    dataIndex: 'count',
  },
];

// 固件任务搜索表单
export const taskSearchSchema: FormSchema[] = [
  {
    field: 'keyword',
    label: '搜索关键字',
    component: 'Input',
  },
  {
    field: '[beginTime,endTime]',
    label: '创建时间',
    component: 'RangePicker',
    componentProps: {
      placeholder: ['创建时间起始', '结束时间截止'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    valueFormat: transformRangePicker,
  },
];

// 固件升级记录
export const taskRecordSchema = (): BasicColumn[] => [
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
  {
    title: '设备序列号',
    dataIndex: 'deviceSn',
  },
  {
    title: '任务名称',
    dataIndex: 'dmsUpdateTask.name',
    customRender: ({ record }) => {
      return get(record, 'dmsUpdateTask.name') || '--';
    },
  },
  {
    title: '设备型号',
    dataIndex: 'deviceModel',
  },
  {
    title: '状态',
    dataIndex: 'status',
    customRender: ({ text }) => {
      return showToBadge({ text, arr: useMapWithI18n(UpgradeStatusList) });
    },
  },
];

// 升级记录搜索表单
export const taskRecordSearchSchema = (): FormSchema[] => [
  {
    field: 'keyword',
    label: '搜索关键字',
    component: 'Input',
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      placeholder: '请选择状态',
      options: useMapWithI18n(UpgradeStatusList),
    },
  },
  {
    field: '[beginTime,endTime]',
    label: '创建时间',
    component: 'RangePicker',
    componentProps: {
      placeholder: ['创建时间起始', '结束时间截止'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    valueFormat: transformRangePicker,
  },
];
