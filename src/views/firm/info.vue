<template>
  <page-wrapper v-loading="loading" @back="go(-1)" title="固件详情">
    <template #header>
      <BasicUser
        :username="get(apiResult, 'name')"
        :description="get(apiResult, 'version')"
        showDes
      >
        <template #extra>
          <a-button type="primary" @click="method.add">添加升级任务</a-button>
        </template>
      </BasicUser>
    </template>

    <div class="flex flex-col gap-4">
      <!-- 固件详情信息卡片 -->
      <a-card :tabList="tabList" v-model:activeTabKey="activeTabKey" @tab-change="onTabChange">
        <Description
          v-if="activeTabKey === 'info'"
          :data="apiResult"
          :schema="basicInfoSchema"
          :column="{ xxl: 3, xl: 3, lg: 3, md: 2, sm: 2, xs: 1 }"
          :bordered="false"
        />
        <BasicTable v-else-if="activeTabKey === 'upgrade'" @register="registerTaskTable">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'ACTION'">
              <TableAction :actions="tableTaskAction(record)" />
            </template>
          </template>
        </BasicTable>
        <BasicTable v-else-if="activeTabKey === 'log'" @register="registerLogTable" />
      </a-card>
    </div>
    <SDrawerForm @register="registerDrawerForm" @success="reload()" />
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { useGo } from '@/hooks/web/usePage';
  import { useRoute } from 'vue-router';
  import { useApiLoading } from '@/hooks/web/useApiLoading';
  import BasicUser from '@/components/Custom/BasicUser.vue';
  import { Description } from '@/components/Description';
  import { get } from 'lodash-es';
  import {
    apiFirmwareStoreInfo,
    apiAddFirmwareTask,
    apiFirmwareTaskPageInfo,
    apiFirmwareRecordPageInfo,
  } from '@/api/op/firm';
  import {
    basicInfoSchema,
    taskSchema,
    taskListSchema,
    taskRecordSchema,
    taskSearchSchema,
    taskRecordSearchSchema,
  } from './info.schema';
  import { computed, ref } from 'vue';
  import { SDrawerForm, useSDrawerForm } from '@/components/SDrawer';
  import { BasicTable, useTable, ActionItem, TableAction } from '@/components/Table';

  const go = useGo();
  const route = useRoute();
  const params = route.params;
  const tabList = [
    {
      key: 'info',
      tab: '固件详情',
    },
    {
      key: 'upgrade',
      tab: '升级任务',
    },
    {
      key: 'log',
      tab: '升级记录',
    },
  ];

  const activeTabKey = ref('info');
  const { reload, loading, apiResult } = useApiLoading({
    api: async () => {
      const id = params.id as string;
      if (!id) return {};
      return await apiFirmwareStoreInfo(id);
    },
    params,
  });

  const onTabChange = (key: string) => {
    activeTabKey.value = key;
  };

  const [registerDrawerForm, { addDrawer }] = useSDrawerForm({
    schemas: taskSchema(),
    addFn: apiAddFirmwareTask,
    addText: '新增升级任务',
  });

  const [registerTaskTable] = useTable({
    api: apiFirmwareTaskPageInfo,
    columns: taskListSchema,
    formConfig: {
      schemas: taskSearchSchema,
    },
    searchInfo: computed(() => {
      return {
        firmwareId: params.id,
      };
    }),
    useSearchForm: true,
    actionColumn: {},
  });
  const [registerLogTable] = useTable({
    api: apiFirmwareRecordPageInfo,
    columns: taskRecordSchema(),
    formConfig: {
      schemas: taskRecordSearchSchema(),
    },
    searchInfo: computed(() => {
      return {
        firmwareId: params.id,
      };
    }),
    useSearchForm: true,
  });

  const method = {
    /** 新增 */
    add: () => {
      addDrawer({
        record: {
          firmwareId: params.id,
        },
      });
    },
    /** 任务详情 */
    detail: (record: Recordable) => {
      go(`/firm/task/${record.id}`);
    },
  };

  function tableTaskAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '详情',
        onClick: method.detail.bind(null, record),
      },
    ];
  }
</script>

<style lang="less" scoped></style>
